# Timezone Conversion Fix

## Problem Identified

The original timezone conversion logic was **incorrect** and could lead to wrong date ranges:

```typescript
// ❌ INCORRECT - This was the original code
const startDate = dayjs(date).tz(timezone).startOf('day').utc().toDate();
const endDate = dayjs(date).tz(timezone).endOf('day').utc().toDate();
```

## Why It Was Wrong

### Example Scenario:
- **Input**: `date = new Date('2025-07-24T00:00:00.000Z')` (UTC midnight)
- **Timezone**: `'America/Chicago'` (UTC-5 in summer)
- **Intended**: Get July 24th in Chicago timezone

### What the Incorrect Code Did:
1. Takes the UTC date `2025-07-24T00:00:00.000Z`
2. **Interprets it as if it were Chicago time** (not UTC)
3. This effectively shifts the date by the timezone offset
4. Result: Wrong date range entirely

### Detailed Example:

```typescript
// ❌ INCORRECT LOGIC
const date = new Date('2025-07-24T00:00:00.000Z'); // July 24th UTC midnight
const timezone = 'America/Chicago';

// This interprets the UTC timestamp AS IF it were Chicago time
const wrongStart = dayjs(date).tz(timezone).startOf('day').utc().toDate();
// Result: 2025-07-24T05:00:00.000Z (July 24th 5 AM UTC)
// This is WRONG - we wanted July 24th 00:00 Chicago time, which is 2025-07-24T05:00:00.000Z

const wrongEnd = dayjs(date).tz(timezone).endOf('day').utc().toDate();
// Result: 2025-07-25T04:59:59.999Z (July 25th 4:59 AM UTC)
// This is WRONG - we wanted July 24th 23:59 Chicago time, which is 2025-07-25T04:59:59.999Z
```

## Correct Solution

```typescript
// ✅ CORRECT - Extract date string and create timezone-specific dates
const dateStr = dayjs(date).format('YYYY-MM-DD'); // Extract just "2025-07-24"
const startDate = dayjs.tz(`${dateStr} 00:00:00`, timezone).utc().toDate();
const endDate = dayjs.tz(`${dateStr} 23:59:59`, timezone).utc().toDate();
```

### Why This Is Correct:

1. **Extract Date String**: `dayjs(date).format('YYYY-MM-DD')` gets just "2025-07-24"
2. **Create Timezone-Specific Dates**: `dayjs.tz('2025-07-24 00:00:00', 'America/Chicago')`
3. **Convert to UTC**: `.utc().toDate()` for database storage

### Detailed Example:

```typescript
// ✅ CORRECT LOGIC
const date = new Date('2025-07-24T00:00:00.000Z'); // July 24th UTC midnight
const timezone = 'America/Chicago';

// Extract just the date part
const dateStr = dayjs(date).format('YYYY-MM-DD'); // "2025-07-24"

// Create July 24th 00:00:00 in Chicago timezone
const correctStart = dayjs.tz(`${dateStr} 00:00:00`, timezone).utc().toDate();
// Result: 2025-07-24T05:00:00.000Z (July 24th midnight Chicago = 5 AM UTC)

// Create July 24th 23:59:59 in Chicago timezone  
const correctEnd = dayjs.tz(`${dateStr} 23:59:59`, timezone).utc().toDate();
// Result: 2025-07-25T04:59:59.000Z (July 24th 11:59 PM Chicago = 4:59 AM UTC next day)
```

## Real-World Impact

### Before Fix (Incorrect):
- User requests "July 24th" analytics in Chicago timezone
- System might return data for July 23rd or July 25th instead
- Date boundaries were shifted by timezone offset

### After Fix (Correct):
- User requests "July 24th" analytics in Chicago timezone  
- System returns data for exactly July 24th 00:00:00 to 23:59:59 Chicago time
- Date boundaries are accurate to user's location

## Files Fixed

### 1. `lib/services/calls-monitoring.ts`

**Before:**
```typescript
const startDate = dayjs(date).tz(timezone).startOf('day').utc().toDate();
const endDate = dayjs(date).tz(timezone).endOf('day').utc().toDate();
```

**After:**
```typescript
const dateStr = dayjs(date).format('YYYY-MM-DD');
const startDate = dayjs.tz(`${dateStr} 00:00:00`, timezone).utc().toDate();
const endDate = dayjs.tz(`${dateStr} 23:59:59`, timezone).utc().toDate();
```

### 2. `pages/api/test/compare-endpoints.ts`

**Before:**
```typescript
const targetDate = dateParam 
  ? dayjs(dateParam).tz(timezone)
  : dayjs().tz(timezone).subtract(1, 'day');

const startDate = targetDate.startOf('day').utc().toDate();
const endDate = targetDate.endOf('day').utc().toDate();
```

**After:**
```typescript
const targetDateStr = dateParam 
  ? dateParam
  : dayjs().tz(timezone).subtract(1, 'day').format('YYYY-MM-DD');

const startDate = dayjs.tz(`${targetDateStr} 00:00:00`, timezone).utc().toDate();
const endDate = dayjs.tz(`${targetDateStr} 23:59:59`, timezone).utc().toDate();
```

## Key Principles

1. **Treat Input Date as Date Reference**: The input date should be treated as "which calendar date" not "specific timestamp"

2. **Extract Date String First**: Use `.format('YYYY-MM-DD')` to get just the date part

3. **Create Timezone-Specific Times**: Use `dayjs.tz('YYYY-MM-DD HH:mm:ss', timezone)` to create exact times in the target timezone

4. **Convert to UTC for Storage**: Use `.utc().toDate()` for database queries

## Testing

You can verify the fix works correctly:

```bash
# Test with the comparison endpoint
GET /api/test/compare-endpoints?date=2025-07-24&excludeZeroDuration=true

# Should show correct UTC ranges for July 24th in your location's timezone
```

## Summary

The timezone conversion fix ensures that:
- **"July 24th"** means exactly July 24th in the user's location
- **Date boundaries** are accurate to the user's timezone
- **Database queries** use the correct UTC ranges
- **Analytics data** reflects the intended date range

This fix eliminates timezone-related discrepancies and ensures users get analytics for the dates they actually request.
