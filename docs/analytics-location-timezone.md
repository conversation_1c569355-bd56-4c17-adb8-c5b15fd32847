# Analytics Dashboard Location-Based Timezone

## Overview

The analytics dashboard has been updated to use the current user's location timezone instead of hardcoded timezones, providing more relevant date boundaries for users in different locations.

## Background

Previously, the analytics dashboard used either hardcoded Eastern Time or UTC for date calculations. This caused confusion when users in different locations viewed analytics data, as "yesterday" or "today" didn't match their local understanding of these time periods.

## Current Implementation

### Location-Based Timezone Resolution

The system now uses `UserLocationService.getUserLocationContext(userId)` to determine the appropriate timezone:

1. **Retrieves User's Current Location**: Gets the user's active location from their profile
2. **Extracts Timezone**: Uses the location's `timeZone` property (e.g., 'America/Chicago')
3. **Fallback Handling**: Defaults to 'America/Chicago' if no location is found or service fails

### Default Location

- **Location ID**: "118" 
- **Timezone**: "America/Chicago"
- **Used When**: No current location is set or location service fails

## Changes Made

### 1. DashboardMetricsService (`lib/services/dashboard-metrics-service.ts`)

**Updated Methods:**
- `getDateBounds(range, timezone)` - Now accepts timezone parameter
- `getSummary(range, filterOptions, timezone)` - Uses location timezone
- `getSummaryForDateRange(start, end, filterOptions, timezone)` - Timezone-aware custom ranges

**Example:**
```typescript
// Before: Hardcoded timezone
const now = dayjs().tz('America/New_York');

// After: Dynamic timezone
const now = dayjs().tz(timezone); // timezone from user's location
```

### 2. CallsMonitoring (`lib/services/calls-monitoring.ts`)

**Updated Methods:**
- `getDailyStats(date, filterOptions, timezone)` - Now timezone-aware

**Example:**
```typescript
// Before: Hardcoded timezone
const startDate = dayjs(date).tz('America/New_York').startOf('day').toDate();

// After: Dynamic timezone  
const startDate = dayjs(date).tz(timezone).startOf('day').utc().toDate();
```

### 3. Dashboard Summary Endpoint (`/api/dashboard/summary`)

**New Logic:**
```typescript
// Get user's current location timezone
let timezone = 'America/Chicago'; // Default fallback
try {
  const locationContext = await UserLocationService.getUserLocationContext(user.id!);
  if (locationContext.currentLocation?.timeZone) {
    timezone = locationContext.currentLocation.timeZone;
  }
} catch (error) {
  // Use fallback timezone
}

// Use timezone in service calls
const summary = await service.getSummary(range, filterOptions, timezone);
```

### 4. Daily Stats Endpoint (`/api/monitoring/daily-stats`)

**Authentication Required**: Now requires user authentication to get location context
**Timezone Integration**: Uses same location resolution logic as dashboard summary

### 5. Test Endpoints

**Compare Endpoints** (`/api/test/compare-endpoints`): Updated to use current location timezone for consistent testing

## Impact on Date Ranges

### Relative Date Ranges

| Range | Before (Eastern Time) | After (Location-Based) |
|-------|----------------------|------------------------|
| "today" | Today in ET | Today in user's location |
| "yesterday" | Yesterday in ET | Yesterday in user's location |
| "week" | This week in ET | This week in user's location |
| "month" | This month in ET | This month in user's location |

### Custom Date Ranges

Custom date ranges (startDate/endDate) are **not affected** by timezone - they use the exact dates provided.

## Examples

### Chicago User (Default)
- **Location**: ID "118", timezone "America/Chicago"
- **"Yesterday"**: July 24th 00:00:00 to 23:59:59 Chicago time
- **Database Query**: Converted to UTC for storage/retrieval

### New York User  
- **Location**: timezone "America/New_York"
- **"Yesterday"**: July 24th 00:00:00 to 23:59:59 New York time
- **Database Query**: Converted to UTC for storage/retrieval

### User Without Location
- **Fallback**: Uses "America/Chicago" timezone
- **Behavior**: Same as Chicago user

## Benefits

1. **User-Centric**: Date ranges match user's local understanding
2. **Location Aware**: Automatically adapts to different office locations
3. **Consistent Experience**: "Today" means today where the user works
4. **Flexible**: Supports multiple timezones across different locations
5. **Robust**: Graceful fallback when location service is unavailable

## Technical Implementation

### Date Calculation Flow

1. **Get User Location**: `UserLocationService.getUserLocationContext(userId)`
2. **Extract Timezone**: `locationContext.currentLocation?.timeZone`
3. **Calculate Date Bounds**: `dayjs().tz(timezone).startOf('day')`
4. **Convert to UTC**: `.utc().toDate()` for database queries
5. **Execute Queries**: Database operations use UTC dates

### Error Handling

- **Location Service Failure**: Falls back to 'America/Chicago'
- **Invalid Timezone**: Falls back to 'America/Chicago'  
- **No Current Location**: Falls back to 'America/Chicago'
- **Authentication Failure**: Returns 401 error

## Migration Notes

- **Existing Data**: Not affected - stored dates remain the same
- **Date Interpretation**: Now depends on user's current location
- **API Compatibility**: Existing API calls work the same way
- **Custom Ranges**: startDate/endDate parameters work exactly as before
- **Fallback Behavior**: System gracefully handles missing location data

## Testing

Use the test endpoint to verify timezone consistency:

```bash
GET /api/test/compare-endpoints?excludeZeroDuration=true
```

This will show:
- User's current location and timezone
- Date range calculations
- Comparison between dashboard summary and daily stats
- Any discrepancies between endpoints

## Future Considerations

- **Multi-Location Users**: Currently uses single "current" location
- **Timezone Changes**: Location timezone updates automatically apply
- **Performance**: Location lookup adds minimal overhead
- **Caching**: Consider caching location context for frequent requests
