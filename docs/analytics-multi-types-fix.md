# Analytics Dashboard Multi-Types Fix

## Problem Description

The analytics dashboard was not properly handling calls with multiple call types after the introduction of the `callTypes` array. The dashboard was only counting the primary `call.type` field and ignoring additional types in the `call.callTypes` array, leading to incorrect analytics data.

### Example Issue

A call with:
```json
{
  "type": 15,
  "callTypes": [15, 5, 9]
}
```

Was only being counted as:
- 1 TRANSFER_DUE_TO_SCHEDULING (type 15)

But should be counted as:
- 1 TRANSFER_DUE_TO_SCHEDULING (type 15)
- 1 RESCHEDULE (type 5) 
- 1 LOOKUP (type 9)

## Root Cause Analysis

### Issues Identified

1. **`countAppointmentsHandledInRange`** (CallsRepository):
   - Only queried `call_type IN (...)` 
   - Ignored `call_types` JSON array
   - Result: Appointment counts were too low

2. **`countAllCallTypesInRange`** (CallsRepository):
   - Had complex logic for single vs multi-type calls
   - Didn't properly count individual types within multi-type calls
   - Result: Issue breakdown was incorrect

3. **`getDailyStats`** (CallsMonitoring):
   - Only used `call.type` for counting
   - Ignored `call.callTypes` array
   - Result: Daily stats were incomplete

## Solution Implementation

### 1. Fixed `countAppointmentsHandledInRange`

**Before:**
```sql
SELECT call_type, COUNT(*) as cnt
FROM calls
WHERE call_date BETWEEN ? AND ? AND call_type IN (?, ?, ?, ?)
GROUP BY call_type
```

**After:**
```sql
SELECT call_type, call_types
FROM calls
WHERE call_date BETWEEN ? AND ?
  AND (
    call_type IN (?, ?, ?, ?) OR
    JSON_CONTAINS(call_types, JSON_ARRAY(?), "$") OR
    JSON_CONTAINS(call_types, JSON_ARRAY(?), "$") OR
    JSON_CONTAINS(call_types, JSON_ARRAY(?), "$") OR
    JSON_CONTAINS(call_types, JSON_ARRAY(?), "$")
  )
```

Then processes each call to count all appointment types found in both `call_type` and `call_types`.

### 2. Fixed `countAllCallTypesInRange`

**Before:** Complex logic with separate queries for single vs multi-type calls.

**After:** Single query that gets all calls and processes each call's complete type set:

```sql
SELECT call_type, call_types
FROM calls
WHERE call_date BETWEEN ? AND ?
```

Then processes each call to count all types found in both `call_type` and `call_types`.

### 3. Fixed `getDailyStats`

**Before:**
```typescript
calls.forEach(call => {
  const callTypeName = getCallTypeName(call.type);
  callTypeBreakdown[callTypeName] = (callTypeBreakdown[callTypeName] || 0) + 1;
});
```

**After:**
```typescript
calls.forEach(call => {
  const callTypes = new Set<number>();
  
  // Add primary call type
  if (call.type !== undefined && call.type !== null) {
    callTypes.add(call.type);
  }
  
  // Add call types from array
  if (call.callTypes && Array.isArray(call.callTypes)) {
    call.callTypes.forEach(type => {
      if (typeof type === 'number' && !isNaN(type)) {
        callTypes.add(type);
      }
    });
  }

  // Count each call type found in this call
  for (const callType of callTypes) {
    const callTypeName = getCallTypeName(callType);
    callTypeBreakdown[callTypeName] = (callTypeBreakdown[callTypeName] || 0) + 1;
  }
});
```

## Key Principles

### 1. Individual Type Counting
Each call type in a call's `callTypes` array is counted individually. A call with `[15, 5, 9]` contributes:
- +1 to TRANSFER_DUE_TO_SCHEDULING
- +1 to RESCHEDULE  
- +1 to LOOKUP

### 2. Deduplication
Uses `Set<number>` to avoid double-counting if the same type appears in both `call.type` and `call.callTypes`.

### 3. Backward Compatibility
Still handles calls that only have `call.type` set (legacy calls) by including the primary type in the counting logic.

### 4. Data Integrity
Validates that call types are numbers and not NaN before counting them.

## Testing

### Test Endpoint
Created `/api/test/analytics-multi-types` to verify the fixes work correctly.

**Usage:**
```bash
GET /api/test/analytics-multi-types?startDate=2025-06-25&endDate=2025-07-25
```

### Expected Results
After the fix, the dashboard should show:
- Higher appointment counts (reschedule, newAppointment, cancel)
- More comprehensive issue breakdown
- Accurate daily stats that reflect all call types

### Verification Steps
1. Run the test endpoint with a date range that has multi-type calls
2. Compare results with the original dashboard summary endpoint
3. Verify that appointment counts and issue counts are now higher and more accurate
4. Check that the total doesn't exceed the number of actual calls (since we're counting types, not calls)

## Impact

### Before Fix
```json
{
  "totalCalls": 416,
  "appointments": {
    "reschedule": 29,
    "newAppointment": 28, 
    "cancel": 19
  },
  "issues": {
    "CONFIRM_APPOINTMENT": 1
  }
}
```

### After Fix (Expected)
```json
{
  "totalCalls": 416,
  "appointments": {
    "reschedule": 45,      // Higher due to multi-type calls
    "newAppointment": 38,  // Higher due to multi-type calls
    "cancel": 25           // Higher due to multi-type calls
  },
  "issues": {
    "TRANSFER_DUE_TO_SCHEDULING": 67,
    "LOOKUP": 89,
    "RESCHEDULE": 45,
    "CONFIRM_APPOINTMENT": 12,
    // ... more comprehensive breakdown
  }
}
```

## Files Modified

1. **`lib/repositories/calls-repository.ts`**:
   - `countAppointmentsHandledInRange()` - Fixed to handle multi-types
   - `countAllCallTypesInRange()` - Simplified and fixed multi-type counting

2. **`lib/services/calls-monitoring.ts`**:
   - `getDailyStats()` - Fixed to count all call types per call

3. **`pages/api/test/analytics-multi-types.ts`** (New):
   - Test endpoint to verify the fixes work correctly

The analytics dashboard should now provide accurate data that reflects the true complexity of calls with multiple types, giving better insights into call patterns and system performance.
