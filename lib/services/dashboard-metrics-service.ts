import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);

import { CallsRepository } from '@/lib/repositories';

export type TimeRange = 'today' | 'yesterday' | 'week' | 'month' | 'custom';

export interface AnalyticsFilterOptions {
  excludeZeroDuration: boolean;
  excludeDisconnected: boolean;
}

export interface DashboardSummary {
  totalCalls: number;
  totalTimeSavedSeconds: number;
  avgTimeSavedSeconds: number;
  appointments: {
    reschedule: number;
    newAppointment: number;
    cancel: number;
  };
  issues: Record<string, number>;
}

/**
 * Service that aggregates KPI metrics for the Analytics Dashboard.
 */
export class DashboardMetricsService {
  private readonly callsRepo: CallsRepository;

  constructor(callsRepository: CallsRepository) {
    this.callsRepo = callsRepository;
  }

  /**
   * Get start & end bounds for requested time range using specified timezone.
   * @param range The time range to calculate
   * @param timezone The timezone to use (defaults to America/Chicago)
   */
  private getDateBounds(range: TimeRange, timezone: string = 'America/Chicago'): { start: Date; end: Date } {
    const now = dayjs().tz(timezone);

    switch (range) {
      case 'today':
        return {
          start: now.startOf('day').utc().toDate(),
          end: now.endOf('day').utc().toDate(),
        };
      case 'yesterday':
        const yesterday = now.subtract(1, 'day');
        return {
          start: yesterday.startOf('day').utc().toDate(),
          end: yesterday.endOf('day').utc().toDate(),
        };
      case 'week':
        return {
          start: now.startOf('week').utc().toDate(), // Monday
          end: now.endOf('week').utc().toDate(),
        };
      case 'month':
        return {
          start: now.startOf('month').utc().toDate(),
          end: now.endOf('month').utc().toDate(),
        };
      default:
        throw new Error(`Invalid time range: ${range}`);
    }
  }

  /**
   * Generate summary for requested time range.
   */
  async getSummary(
    range: TimeRange,
    filterOptions: AnalyticsFilterOptions = {
      excludeZeroDuration: false,
      excludeDisconnected: false,
    },
    timezone: string = 'America/Chicago',
  ): Promise<DashboardSummary> {
    const { start, end } = this.getDateBounds(range, timezone);

    console.log(`📊 Dashboard metrics for range '${range}' (${timezone}):`, {
      startDate: start.toISOString(),
      endDate: end.toISOString(),
      filterOptions,
      timezone,
    });

    const [totalCalls, totalSeconds, appointments, issues] = await Promise.all([
      this.callsRepo.countCallsInRange(start, end, filterOptions),
      this.callsRepo.sumDurationsInRange(start, end, filterOptions),
      this.callsRepo.countAppointmentsHandledInRange(start, end, filterOptions),
      this.callsRepo.countAllCallTypesInRange(start, end, filterOptions),
    ]);

    const avgSeconds = totalCalls > 0 ? Math.round(totalSeconds / totalCalls) : 0;

    const summary = {
      totalCalls,
      totalTimeSavedSeconds: totalSeconds,
      avgTimeSavedSeconds: avgSeconds,
      appointments,
      issues,
    };

    console.log(`📊 Dashboard metrics result:`, summary);

    return summary;
  }

  /**
   * Generate summary for custom date range.
   */
  async getSummaryForDateRange(
    startDate: Date,
    endDate: Date,
    filterOptions: AnalyticsFilterOptions = {
      excludeZeroDuration: false,
      excludeDisconnected: false,
    },
    timezone?: string,
  ): Promise<DashboardSummary> {
    console.log(`📊 Dashboard metrics for custom range:`, {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      filterOptions,
      timezone: timezone || 'custom range (no timezone conversion)',
    });

    const [totalCalls, totalSeconds, appointments, issues] = await Promise.all([
      this.callsRepo.countCallsInRange(startDate, endDate, filterOptions),
      this.callsRepo.sumDurationsInRange(startDate, endDate, filterOptions),
      this.callsRepo.countAppointmentsHandledInRange(startDate, endDate, filterOptions),
      this.callsRepo.countAllCallTypesInRange(startDate, endDate, filterOptions),
    ]);

    const avgSeconds = totalCalls > 0 ? Math.round(totalSeconds / totalCalls) : 0;

    const summary = {
      totalCalls,
      totalTimeSavedSeconds: totalSeconds,
      avgTimeSavedSeconds: avgSeconds,
      appointments,
      issues,
    };

    console.log(`📊 Dashboard metrics result:`, summary);

    return summary;
  }
}
