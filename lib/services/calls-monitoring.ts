import { Call } from '@/models/Call';
import { CallType } from '@/models/CallTypes';
import { callsService } from '@/utils/firestore';
import { CallSummarizationService } from './call-summarization';
import { getRepositories } from '@/lib/repositories';
import { AnalyticsFilterOptions } from '@/lib/services/dashboard-metrics-service';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Service for monitoring and tracking call-related metrics
 */
export class CallsMonitoring {
  private static instances = new Map<string, CallsMonitoring>();
  private readonly clinicId: number;
  private readonly locationId: string;

  private constructor(clinicId: number, locationId: string) {
    this.clinicId = clinicId;
    this.locationId = locationId;
  }

  public static getInstance(clinicId: number, locationId: string): CallsMonitoring {
    const key = `${clinicId}-${locationId}`;
    if (!CallsMonitoring.instances.has(key)) {
      CallsMonitoring.instances.set(key, new CallsMonitoring(clinicId, locationId));
    }
    return CallsMonitoring.instances.get(key)!;
  }

  /**
   * Helper method to fetch all calls by automatically handling pagination
   */
  private async getAllCalls(params: {
    startDate?: Date;
    endDate?: Date;
    searchTerm?: string;
    callType?: CallType;
    minPriority?: number;
    maxPriority?: number;
    officeHoursOnly?: boolean;
    callDirection?: 'inbound' | 'outbound' | 'both';
    includeUnknownPhoneNumbers?: boolean;
  }): Promise<Call[]> {
    const allCalls: Call[] = [];
    let lastDocId: string | undefined;
    let isLastPage = false;
    const pageSize = 1000; // Fetch in batches of 1000

    while (!isLastPage) {
      const result = await callsService.paginateCalls({
        limit: pageSize,
        startAfterId: lastDocId,
        clinicId: this.clinicId,
        locationId: this.locationId,
        ...params,
      });

      allCalls.push(...result.calls);
      lastDocId = result.lastDocId;
      isLastPage = result.isLastPage;

      // Safety check to prevent infinite loops
      if (result.calls.length === 0) {
        break;
      }
    }

    return allCalls;
  }

  /**
   * Converts human-readable duration strings to seconds
   * @param duration - Duration string like "17 sec", "2.2 min", "0 sec", etc.
   * @returns Duration in seconds as a number
   */
  private convertDurationToSeconds(duration: string): number {
    try {
      if (!duration || typeof duration !== 'string') {
        return 0;
      }

      const trimmedDuration = duration.trim().toLowerCase();

      // Handle "0 sec" or "0 min" cases
      if (trimmedDuration === '0 sec' || trimmedDuration === '0 min') {
        return 0;
      }

      // Extract number and unit using regex
      const match = trimmedDuration.match(/^(\d+(?:\.\d+)?)\s*(sec|min)$/);

      if (!match) {
        // If format doesn't match expected pattern, return 0
        return 0;
      }

      const value = parseFloat(match[1]);
      const unit = match[2];

      if (isNaN(value)) {
        return 0;
      }

      // Convert to seconds
      if (unit === 'min') {
        return Math.round(value * 60);
      } else if (unit === 'sec') {
        return Math.round(value);
      }

      return 0;
    } catch (error) {
      // Log error for debugging (optional - you can remove this if you don't want logging)
      console.warn('Error converting duration to seconds:', { duration, error });
      return 0;
    }
  }

  /**
   * Determines if a call represents a potential issue based on business rules
   * @param call - The call to evaluate
   * @returns True if the call is considered a potential issue
   */
  private isPotentialIssue(call: Call): boolean {
    const durationInSeconds = this.convertDurationToSeconds(call.duration || '0 sec');
    // If call time is more than 60 seconds and it was transferred to human, it's a potential issue
    return durationInSeconds > 60 && call.type == CallType.TRANSFER_TO_HUMAN;
  }

  private async summarizeCall(callId: string): Promise<string> {
    try {
      const summarizationService = CallSummarizationService.getInstance();
      return await summarizationService.summarizeCallById(callId);
    } catch (error) {
      // Return user-friendly error messages for monitoring purposes
      if (error instanceof Error) {
        return error.message;
      }
      return 'Call summary not available';
    }
  }

  public async getHourlyStats(date: Date): Promise<{
    totalCalls: number;
    calls: (Call & { summary: string; isPotentialIssue: boolean })[];
  }> {
    const calls = await this.getAllCalls({
      startDate: dayjs(date).utc().subtract(1, 'hour').startOf('hour').toDate(),
      endDate: dayjs(date).utc().subtract(1, 'hour').endOf('hour').toDate(),
    });

    const callsWithSummaries = await Promise.all(
      calls.map(async call => ({
        ...call,
        summary: call.summary || (await this.summarizeCall(call.id)),
        isPotentialIssue: this.isPotentialIssue(call),
      })),
    );

    return {
      totalCalls: calls.length,
      calls: callsWithSummaries,
    };
  }

  public async getDailyStats(
    date: Date,
    filterOptions: AnalyticsFilterOptions = {
      excludeZeroDuration: false,
      excludeDisconnected: false,
    },
    timezone: string = 'America/Chicago',
  ): Promise<{
    totalCalls: number;
    potentialIssuesCount: number;
    callTypeBreakdown: Record<string, number>;
  }> {
    const startDate = dayjs(date).tz(timezone).startOf('day').utc().toDate();
    const endDate = dayjs(date).tz(timezone).endOf('day').utc().toDate();

    // Use the repository methods that respect filter options instead of getAllCalls
    const repoManager = getRepositories();
    await repoManager.initialize();

    // Get filtered data using repository methods (same as dashboard summary)
    const [totalCalls, callTypeBreakdown] = await Promise.all([
      repoManager.calls.countCallsInRange(startDate, endDate, filterOptions),
      repoManager.calls.countAllCallTypesInRange(startDate, endDate, filterOptions),
    ]);

    // For potential issues, we still need to get the actual calls to check duration/type
    // But we'll apply the same filters
    const calls = await this.getAllCalls({
      startDate,
      endDate,
    });

    // Apply the same filters that the repository uses
    const filteredCalls = calls.filter(call => {
      // Apply excludeZeroDuration filter
      if (filterOptions.excludeZeroDuration) {
        // Exclude all calls with zero duration
        const isZeroDuration = ['0 min', '0 mins', '0 sec', '0 secs', '', null, undefined].includes(call.duration);

        if (isZeroDuration) {
          return false; // Exclude this call
        }
      }

      // Apply excludeDisconnected filter
      if (filterOptions.excludeDisconnected) {
        const hasDisconnectedType = call.type === CallType.DISCONNECTED;
        const hasDisconnectedInCallTypes = call.callTypes &&
          Array.isArray(call.callTypes) &&
          call.callTypes.includes(CallType.DISCONNECTED);

        if (hasDisconnectedType || hasDisconnectedInCallTypes) {
          return false; // Exclude this call
        }
      }

      return true; // Include this call
    });

    // Count potential issues using the filtered calls
    let potentialIssuesCount = 0;
    filteredCalls.forEach(call => {
      if (this.isPotentialIssue(call)) {
        potentialIssuesCount++;
      }
    });

    // Sort call types by count in descending order
    const sortedCallTypeBreakdown: Record<string, number> = {};
    Object.entries(callTypeBreakdown)
      .sort(([, a], [, b]) => b - a) // Sort by count descending
      .forEach(([callTypeName, count]) => {
        sortedCallTypeBreakdown[callTypeName] = count;
      });

    return {
      totalCalls,
      potentialIssuesCount,
      callTypeBreakdown: sortedCallTypeBreakdown,
    };
  }
}
