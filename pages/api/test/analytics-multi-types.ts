import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getRepositories } from '@/lib/repositories';
import { DashboardMetricsService } from '@/lib/services/dashboard-metrics-service';

/**
 * @swagger
 * /api/test/analytics-multi-types:
 *   get:
 *     summary: Test analytics with multiple call types
 *     description: Test endpoint to verify that analytics properly handle calls with multiple call types
 *     tags: [Test, Analytics]
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *         required: true
 *         description: Start date in YYYY-MM-DD format
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *         required: true
 *         description: End date in YYYY-MM-DD format
 *     responses:
 *       200:
 *         description: Analytics test results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 analytics:
 *                   type: object
 *                 debug:
 *                   type: object
 *       400:
 *         description: Bad request - missing parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * Test endpoint for analytics with multiple call types
 * 
 * This endpoint tests the fixed analytics logic to ensure it properly handles
 * calls with multiple call types in the callTypes array.
 * 
 * @route GET /api/test/analytics-multi-types
 * 
 * @param {string} startDate - Required. Start date in YYYY-MM-DD format
 * @param {string} endDate - Required. End date in YYYY-MM-DD format
 * 
 * @returns {Object} 200 - Test results with analytics data and debug info
 * 
 * @example
 * GET /api/test/analytics-multi-types?startDate=2025-06-25&endDate=2025-07-25
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get parameters
    const startDateStr = req.query.startDate as string;
    const endDateStr = req.query.endDate as string;

    if (!startDateStr || !endDateStr) {
      return res.status(400).json({ error: 'startDate and endDate are required' });
    }

    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    console.log(`🧪 Testing analytics for date range: ${startDateStr} to ${endDateStr}`);

    // Initialize repositories
    const repoManager = getRepositories();
    await repoManager.initialize();

    // Test with excludeZeroDuration=true (same as the failing request)
    const filterOptions = {
      excludeZeroDuration: true,
      excludeDisconnected: false,
    };

    // Get analytics using the dashboard service
    const service = new DashboardMetricsService(repoManager.calls);
    const analytics = await service.getSummaryForDateRange(startDate, endDate, filterOptions);

    // Get some debug information
    const debugInfo = {
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
      },
      filterOptions,
      analyticsKeys: Object.keys(analytics),
      appointmentTypes: Object.keys(analytics.appointments),
      issueTypes: Object.keys(analytics.issues),
      totalIssueCount: Object.values(analytics.issues).reduce((sum, count) => sum + count, 0),
    };

    console.log(`🧪 Analytics test results:`, {
      totalCalls: analytics.totalCalls,
      appointments: analytics.appointments,
      issuesCount: Object.keys(analytics.issues).length,
      debugInfo,
    });

    return res.status(200).json({
      success: true,
      message: 'Analytics test completed successfully',
      analytics,
      debug: debugInfo,
    });

  } catch (error) {
    console.error('Error in analytics multi-types test:', error);
    
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
