import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getRepositories } from '@/lib/repositories';
import { DashboardMetricsService } from '@/lib/services/dashboard-metrics-service';
import { CallsMonitoring } from '@/lib/services/calls-monitoring';
import { UserLocationService } from '@/lib/services/userLocationService';
import { URMA_CLINIC_ID, URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * @swagger
 * /api/test/compare-endpoints:
 *   get:
 *     summary: Compare dashboard summary vs daily stats endpoints
 *     description: Test endpoint to verify both endpoints return consistent data with same filters
 *     tags: [Test, Analytics]
 *     parameters:
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *         required: false
 *         description: Date in YYYY-MM-DD format (defaults to yesterday)
 *       - in: query
 *         name: excludeZeroDuration
 *         schema:
 *           type: boolean
 *         required: false
 *         description: Whether to exclude zero duration calls
 *       - in: query
 *         name: excludeDisconnected
 *         schema:
 *           type: boolean
 *         required: false
 *         description: Whether to exclude disconnected calls
 *     responses:
 *       200:
 *         description: Comparison results
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * Test endpoint to compare dashboard summary vs daily stats endpoints
 * 
 * This endpoint helps debug discrepancies between the two analytics endpoints
 * by running both with the same date range and filter options.
 * 
 * @route GET /api/test/compare-endpoints
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get parameters
    const dateParam = req.query.date as string;
    const excludeZeroDuration = req.query.excludeZeroDuration === 'true';
    const excludeDisconnected = req.query.excludeDisconnected === 'true';

    // Get user's current location timezone
    let timezone = 'America/Chicago'; // Default fallback
    try {
      const locationContext = await UserLocationService.getUserLocationContext(user.id!);
      if (locationContext.currentLocation?.timeZone) {
        timezone = locationContext.currentLocation.timeZone;
        console.log(`📍 Test using location timezone: ${timezone} (Location: ${locationContext.currentLocation.name})`);
      } else {
        console.log(`📍 No current location found for test, using default timezone: ${timezone}`);
      }
    } catch (error) {
      console.warn(`📍 Failed to get user location for test, using default timezone: ${timezone}`, error);
    }

    // Default to yesterday if no date provided
    const targetDateStr = dateParam
      ? dateParam
      : dayjs().tz(timezone).subtract(1, 'day').format('YYYY-MM-DD');

    // Validate date format
    const targetDate = dayjs(targetDateStr);
    if (!targetDate.isValid()) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    console.log(`🔍 Comparing endpoints for date: ${targetDateStr} (${timezone})`);
    console.log(`🔍 Filters: excludeZeroDuration=${excludeZeroDuration}, excludeDisconnected=${excludeDisconnected}`);

    // Calculate date bounds - create specific timezone dates for the target date
    const startDate = dayjs.tz(`${targetDateStr} 00:00:00`, timezone).utc().toDate();
    const endDate = dayjs.tz(`${targetDateStr} 23:59:59`, timezone).utc().toDate();

    console.log(`🔍 Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    // Initialize repositories
    const repoManager = getRepositories();
    await repoManager.initialize();

    const filterOptions = {
      excludeZeroDuration,
      excludeDisconnected,
    };

    // 1. Get data using dashboard summary logic
    const dashboardService = new DashboardMetricsService(repoManager.calls);
    const dashboardSummary = await dashboardService.getSummaryForDateRange(
      startDate,
      endDate,
      filterOptions,
      timezone
    );

    // 2. Get data using daily stats logic
    const monitoringService = CallsMonitoring.getInstance(URMA_CLINIC_ID, URMA_LOMBARD_LOCATION_ID);
    const dailyStats = await monitoringService.getDailyStats(targetDate.toDate(), filterOptions, timezone);

    // 3. Compare the results
    const comparison = {
      dateInfo: {
        inputDate: dateParam || 'yesterday',
        resolvedDate: targetDate.format('YYYY-MM-DD'),
        timezone: timezone,
        utcRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
        },
      },
      filterOptions,
      dashboardSummary: {
        totalCalls: dashboardSummary.totalCalls,
        appointments: dashboardSummary.appointments,
        issuesCount: Object.keys(dashboardSummary.issues).length,
        topIssues: Object.entries(dashboardSummary.issues)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5)
          .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {}),
      },
      dailyStats: {
        totalCalls: dailyStats.totalCalls,
        potentialIssuesCount: dailyStats.potentialIssuesCount,
        callTypeCount: Object.keys(dailyStats.callTypeBreakdown).length,
        topCallTypes: Object.entries(dailyStats.callTypeBreakdown)
          .slice(0, 5)
          .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {}),
      },
      discrepancies: {
        totalCallsDiff: dashboardSummary.totalCalls - dailyStats.totalCalls,
        issuesVsCallTypes: {
          dashboardIssuesTotal: Object.values(dashboardSummary.issues).reduce((sum, count) => sum + count, 0),
          dailyStatsCallTypesTotal: Object.values(dailyStats.callTypeBreakdown).reduce((sum, count) => sum + count, 0),
        },
      },
    };

    // 4. Check for major discrepancies
    const hasDiscrepancies = Math.abs(comparison.discrepancies.totalCallsDiff) > 0;

    console.log(`🔍 Comparison results:`, {
      hasDiscrepancies,
      totalCallsDiff: comparison.discrepancies.totalCallsDiff,
      dashboardTotal: dashboardSummary.totalCalls,
      dailyStatsTotal: dailyStats.totalCalls,
    });

    return res.status(200).json({
      success: true,
      message: hasDiscrepancies 
        ? 'Discrepancies found between endpoints' 
        : 'Endpoints are consistent',
      hasDiscrepancies,
      comparison,
      // Include full data for debugging if there are discrepancies
      ...(hasDiscrepancies && {
        fullDashboardSummary: dashboardSummary,
        fullDailyStats: dailyStats,
      }),
    });

  } catch (error) {
    console.error('Error in compare-endpoints test:', error);
    
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
