import { NextApiRequest, NextApiResponse } from 'next';
import { CallsMonitoring } from '@/lib/services/calls-monitoring';
import { URMA_CLINIC_ID, URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import { mailService, userService } from '@/utils/firestore';
import { AppLinkBuilder } from '@/utils/app-link.builder';
import { AnalyticsFilterOptions } from '@/lib/services/dashboard-metrics-service';
import { UserLocationService } from '@/lib/services/userLocationService';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import dayjs from 'dayjs';
import logger from '@/lib/external-api/v2/utils/logger';

interface ApiResponse {
  message?: string;
  error?: string;
  data?: unknown;
}

interface DailyStatsRequest {
  date?: string; // ISO date string (optional, defaults to current date/time)
  clinicId?: number;
  locationId?: string;
  excludeZeroDuration?: boolean; // Filter option to exclude zero duration calls
  excludeDisconnected?: boolean; // Filter option to exclude disconnected calls
}

/**
 * @swagger
 * /api/monitoring/daily-stats:
 *   post:
 *     summary: Get daily call statistics
 *     description: Retrieves comprehensive call statistics for a specific day, including call type breakdowns and potential issues count
 *     tags: [Monitoring]
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: []
 *             properties:
 *               date:
 *                 type: string
 *                 format: date-time
 *                 description: ISO date string for the day to analyze (optional, defaults to current date/time)
 *                 example: "2024-01-15T00:00:00.000Z"
 *               clinicId:
 *                 type: number
 *                 description: Clinic ID (optional, defaults to URMA_CLINIC_ID)
 *                 example: 12
 *               locationId:
 *                 type: string
 *                 description: Location ID (optional, defaults to URMA_LOMBARD_LOCATION_ID)
 *                 example: "118"
               excludeZeroDuration:
                 type: boolean
                 description: Whether to exclude calls with zero duration (optional, defaults to false)
                 example: true
               excludeDisconnected:
                 type: boolean
                 description: Whether to exclude disconnected calls (optional, defaults to false)
                 example: false
 *     responses:
 *       200:
 *         description: Daily call statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalCalls:
 *                       type: number
 *                       description: Total number of calls for the day
 *                       example: 45
 *                     potentialIssuesCount:
 *                       type: number
 *                       description: Number of calls identified as potential issues
 *                       example: 3
 *                     callTypeBreakdown:
 *                       type: object
 *                       description: Map of call type names to their counts
 *                       additionalProperties:
 *                         type: number
 *                       example:
 *                         "New Patient New Appointment": 12
 *                         "New Appointment Existing Patient": 8
 *                         "Reschedule": 5
 *                         "Cancellation": 2
 *                         "General Info": 18
 *       400:
 *         description: Bad request - missing or invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Missing required field: date"
 *       405:
 *         description: Method not allowed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Method not allowed"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch daily stats"
 *                 message:
 *                   type: string
 *                   description: Detailed error message
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify authentication to get user for location context
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const { date, clinicId, locationId, excludeZeroDuration, excludeDisconnected } = req.body as DailyStatsRequest;

    // Use current date/time if not provided
    const dateToUse = date || new Date().toISOString();

    const parsedDate = new Date(dateToUse);
    if (isNaN(parsedDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format' });
    }

    // Use default values if not provided
    const finalClinicId = clinicId ?? URMA_CLINIC_ID;
    const finalLocationId = locationId ?? URMA_LOMBARD_LOCATION_ID;

    // Get user's current location timezone
    let timezone = 'America/Chicago'; // Default fallback
    try {
      const locationContext = await UserLocationService.getUserLocationContext(user.id!);
      if (locationContext.currentLocation?.timeZone) {
        timezone = locationContext.currentLocation.timeZone;
        console.log(`📍 Daily stats using location timezone: ${timezone} (Location: ${locationContext.currentLocation.name})`);
      } else {
        console.log(`📍 No current location found for daily stats, using default timezone: ${timezone}`);
      }
    } catch (error) {
      console.warn(`📍 Failed to get user location for daily stats, using default timezone: ${timezone}`, error);
    }

    // Prepare filter options
    const filterOptions: AnalyticsFilterOptions = {
      excludeZeroDuration: excludeZeroDuration ?? false,
      excludeDisconnected: excludeDisconnected ?? false,
    };

    const monitoringService = CallsMonitoring.getInstance(finalClinicId, finalLocationId);
    const stats = await monitoringService.getDailyStats(parsedDate, filterOptions, timezone);

    // Get the staff member ids
    const staffMemberIds = await userService.getNotifiableStaffIdsForDailyMonitoring({
      locationId: finalLocationId,
      clinicId: finalClinicId,
    });
    if (!staffMemberIds.length) {
      logger.info(
        {
          context: 'Email Notification Service',
        },
        'No staff members found for daily monitoring notification',
      );
      return res.status(200).json({ data: stats });
    }

    const appLinkBuilder = AppLinkBuilder.getInstance();
    const dashboardLink = appLinkBuilder.getDashboardLink();

    // todo vk: uncomment
    // Send the email to the staff members
    // await mailService.sendDailyMonitoringEmail(staffMemberIds, {
    //   ctaLink: dashboardLink,
    //   reportDate: dayjs(parsedDate).format('MMM D, YYYY'),
    //   ...stats,
    // });

    return res.status(200).json({ data: stats });
  } catch (error) {
    console.error('Error fetching daily stats:', error);
    return res.status(500).json({
      error: 'Failed to fetch daily stats',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
